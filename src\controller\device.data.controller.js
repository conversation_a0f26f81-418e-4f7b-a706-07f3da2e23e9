const excelJS = require('exceljs')
const path = require('path')
const fs = require('fs')

class DeviceDataController {
  // 处理上传的Excel文件并转换数据
  async processExcelData(ctx) {
    try {
      const { file } = ctx.request.files
      if (!file) {
        return (ctx.body = { code: 400, message: '请上传Excel文件' })
      }

      // 读取Excel文件
      const workbook = new excelJS.Workbook()
      await workbook.xlsx.readFile(file.filepath)
      
      const worksheet = workbook.getWorksheet(1) // 获取第一个工作表
      if (!worksheet) {
        return (ctx.body = { code: 400, message: 'Excel文件格式错误' })
      }

      // 解析数据
      const originalData = this.parseWorksheetData(worksheet)
      if (originalData.length === 0) {
        return (ctx.body = { code: 400, message: '未找到有效数据或缺少必要字段（采集时间、末端压力）' })
      }

      // 处理数据：6秒颗粒度转换为1分钟颗粒度
      const processedData = this.processDeviceData(originalData)

      // 清理临时文件
      if (fs.existsSync(file.filepath)) {
        fs.unlinkSync(file.filepath)
      }

      ctx.body = {
        code: 200,
        message: '数据处理成功',
        data: {
          originalCount: originalData.length,
          processedCount: processedData.length,
          originalData: originalData.slice(0, 100), // 只返回前100条原始数据用于预览
          processedData: processedData,
          timeRange: this.getTimeRange(originalData)
        }
      }
    } catch (error) {
      console.error('处理Excel数据失败:', error)
      ctx.body = { code: 500, message: '处理失败: ' + error.message }
    }
  }

  // 导出处理后的数据
  async exportProcessedData(ctx) {
    try {
      const { data } = ctx.request.body
      if (!data || !Array.isArray(data) || data.length === 0) {
        return (ctx.body = { code: 400, message: '没有可导出的数据' })
      }

      // 创建工作簿
      const workbook = new excelJS.Workbook()
      const worksheet = workbook.addWorksheet('处理后数据')

      // 设置表头
      worksheet.columns = [
        { header: '序号', key: 'index', width: 8 },
        { header: '采集时间', key: 'time', width: 20 },
        { header: '末端压力(MPa)', key: 'pressure', width: 15 },
        { header: '数据来源', key: 'source', width: 12 }
      ]

      // 添加数据
      data.forEach((item, index) => {
        worksheet.addRow({
          index: index + 1,
          time: item.time,
          pressure: item.pressure,
          source: item.source === 'original' ? '原始数据' : '补充数据'
        })
      })

      // 设置样式
      worksheet.getRow(1).font = { bold: true }
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
      }

      // 生成文件
      const fileName = `设备数据_处理后_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.xlsx`
      const filePath = path.join(__dirname, `../../updates/file/${fileName}`)
      
      // 确保目录存在
      const dir = path.dirname(filePath)
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true })
      }

      await workbook.xlsx.writeFile(filePath)

      ctx.body = {
        code: 200,
        message: '导出成功',
        data: {
          fileName,
          downloadUrl: `${ctx.origin}/nodeServer/file/${fileName}`,
          recordCount: data.length
        }
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      ctx.body = { code: 500, message: '导出失败: ' + error.message }
    }
  }

  // 解析工作表数据
  parseWorksheetData(worksheet) {
    const data = []
    let timeColumnIndex = -1
    let pressureColumnIndex = -1

    // 查找表头行，确定采集时间和末端压力列的位置
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) {
        // 假设第一行是表头
        row.eachCell((cell, colNumber) => {
          const cellValue = cell.value ? cell.value.toString().trim() : ''
          if (cellValue.includes('采集时间') || cellValue.includes('时间')) {
            timeColumnIndex = colNumber
          }
          if (cellValue.includes('末端压力') || cellValue.includes('压力')) {
            pressureColumnIndex = colNumber
          }
        })
      } else if (rowNumber > 1 && timeColumnIndex > 0 && pressureColumnIndex > 0) {
        // 数据行
        const timeCell = row.getCell(timeColumnIndex)
        const pressureCell = row.getCell(pressureColumnIndex)

        if (timeCell.value && pressureCell.value !== null && pressureCell.value !== undefined) {
          data.push({
            time: this.parseExcelTime(timeCell.value),
            pressure: parseFloat(pressureCell.value) || 0,
            rowIndex: rowNumber
          })
        }
      }
    })

    return data
  }

  // 解析Excel时间格式
  parseExcelTime(timeValue) {
    if (typeof timeValue === 'number') {
      // Excel日期序列号
      const date = new Date((timeValue - 25569) * 86400 * 1000)
      return date
    } else if (timeValue instanceof Date) {
      return timeValue
    } else if (typeof timeValue === 'string') {
      return new Date(timeValue)
    }
    return new Date()
  }

  // 处理设备数据：6秒颗粒度转换为1分钟颗粒度
  processDeviceData(originalData) {
    if (originalData.length === 0) return []

    // 按时间排序
    const sortedData = originalData.slice().sort((a, b) => a.time - b.time)
    
    const result = []
    const startTime = sortedData[0].time
    const endTime = sortedData[sortedData.length - 1].time

    // 获取开始日期的00:00:00
    const dayStart = new Date(startTime)
    dayStart.setHours(0, 0, 0, 0)

    let lastValidPressure = 0
    let dataIndex = 0

    // 生成一天的每分钟数据（1440分钟）
    for (let minute = 0; minute < 1440; minute++) {
      const currentMinute = new Date(dayStart.getTime() + minute * 60 * 1000)
      const minuteStart = currentMinute.getTime()
      const minuteEnd = minuteStart + 60 * 1000

      // 查找这一分钟内的第一条数据
      let foundData = null
      for (let i = dataIndex; i < sortedData.length; i++) {
        const dataTime = sortedData[i].time.getTime()
        
        if (dataTime >= minuteStart && dataTime < minuteEnd) {
          foundData = sortedData[i]
          dataIndex = i
          break
        } else if (dataTime >= minuteEnd) {
          break
        }
      }

      let pressure
      if (foundData) {
        pressure = foundData.pressure / 1000 // 除以1000转换单位
        lastValidPressure = pressure
      } else {
        // 使用上一次的有效数据补充
        pressure = lastValidPressure
      }

      result.push({
        time: this.formatDateTime(currentMinute),
        pressure: pressure.toFixed(3),
        source: foundData ? 'original' : 'interpolated'
      })
    }

    return result
  }

  // 格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  // 获取时间范围
  getTimeRange(data) {
    if (data.length === 0) return null
    
    const times = data.map(item => item.time).sort((a, b) => a - b)
    return {
      start: this.formatDateTime(times[0]),
      end: this.formatDateTime(times[times.length - 1])
    }
  }
}

module.exports = new DeviceDataController()
