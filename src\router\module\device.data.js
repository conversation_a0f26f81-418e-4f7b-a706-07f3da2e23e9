const Router = require('@koa/router')
const { koaBody } = require('koa-body')
const path = require('path')
const controller = require('../../controller/device.data.controller.js')

const router = new Router({ prefix: '/deviceData' })

// 文件上传中间件配置
const uploadMiddleware = koaBody({
  multipart: true,
  formidable: {
    uploadDir: path.join(__dirname, '../../../updates/temp'),
    keepExtensions: true,
    maxFileSize: 50 * 1024 * 1024, // 50MB
    filter: ({ name, originalFilename, mimetype }) => {
      // 只允许Excel文件
      return (
        mimetype &&
        (mimetype.includes('spreadsheet') ||
          mimetype.includes('excel') ||
          originalFilename.endsWith('.xlsx') ||
          originalFilename.endsWith('.xls'))
      )
    }
  }
})

// 处理Excel数据
router.post('/process', uploadMiddleware, controller.processExcelData)

// 导出处理后的数据
router.post('/export', controller.exportProcessedData)

module.exports = router
